.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  padding: 20px;
  overflow: hidden;
}

/* 科技感背景图案 */
.login-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 50px 50px, 25px 25px;
  background-position: 0 0, 12.5px 12.5px;
  z-index: 0;
  animation: patternPulse 20s infinite ease-in-out;
}

/* 网格线效果 */
.login-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 0;
}

@keyframes patternPulse {
  0%, 100% {
    opacity: 0.6;
    background-size: 50px 50px, 25px 25px;
  }
  50% {
    opacity: 0.8;
    background-size: 52px 52px, 26px 26px;
  }
}

/* 粒子效果容器 */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
  pointer-events: none;
}

.particle:nth-child(1) {
  width: 12px;
  height: 12px;
  top: 10%;
  left: 20%;
  animation: float 25s infinite ease-in-out;
}

.particle:nth-child(2) {
  width: 16px;
  height: 16px;
  top: 30%;
  left: 40%;
  animation: float 20s infinite ease-in-out;
  animation-delay: -2s;
}

.particle:nth-child(3) {
  width: 10px;
  height: 10px;
  top: 60%;
  left: 10%;
  animation: float 22s infinite ease-in-out;
  animation-delay: -5s;
}

.particle:nth-child(4) {
  width: 14px;
  height: 14px;
  top: 80%;
  left: 70%;
  animation: float 18s infinite ease-in-out;
  animation-delay: -7s;
}

.particle:nth-child(5) {
  width: 8px;
  height: 8px;
  top: 40%;
  left: 80%;
  animation: float 24s infinite ease-in-out;
  animation-delay: -3s;
}

.particle:nth-child(6) {
  width: 18px;
  height: 18px;
  top: 20%;
  left: 60%;
  animation: float 21s infinite ease-in-out;
  animation-delay: -8s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-70px) translateX(35px);
    opacity: 1;
  }
  50% {
    transform: translateY(-35px) translateX(-35px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(70px) translateX(20px);
    opacity: 1;
  }
}

.login-background {
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 1;
}

.login-content {
  position: relative;
  z-index: 1;
}

.login-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo-container {
  margin-bottom: 16px;
}

.login-logo {
  height: 64px;
  width: auto;
}

.login-title {
  color: #333 !important;
  margin-bottom: 8px !important;
  font-weight: 600;
  font-size: 28px;
}

.login-subtitle {
  font-size: 14px;
  color: #666;
}

.login-form {
  margin-bottom: 24px;
}

.login-form .ant-form-item {
  margin-bottom: 20px;
}

.login-form .ant-input-affix-wrapper,
.login-form .ant-input-password {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
  background: rgba(255, 255, 255, 0.9);
}

.login-form .ant-input-affix-wrapper:hover,
.login-form .ant-input-password:hover {
  border-color: #666 !important;
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused,
.login-form .ant-input-password:focus,
.login-form .ant-input-password-focused {
  border-color: #333 !important;
  box-shadow: 0 0 0 2px rgba(51, 51, 51, 0.2) !important;
}

.login-button {
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #333 0%, #000 100%) !important;
  border: none !important;
  transition: all 0.3s;
  color: white !important;
}

.login-button:hover,
.login-button:focus {
  background: linear-gradient(135deg, #555 0%, #333 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  border-color: transparent !important;
  color: white !important;
}

.login-button:active {
  transform: translateY(0);
  background: linear-gradient(135deg, #222 0%, #000 100%) !important;
}

.login-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-card {
    padding: 16px;
  }
  
  .login-title {
    font-size: 24px;
  }
}

/* 加载状态动画 */
.login-button.ant-btn-loading {
  background: linear-gradient(135deg, #333 0%, #000 100%);
}

/* 表单验证错误样式 */
.login-form .ant-form-item-has-error .ant-input-affix-wrapper,
.login-form .ant-form-item-has-error .ant-input-password {
  border-color: #ff4d4f;
}

.login-form .ant-form-item-has-error .ant-input-affix-wrapper:focus,
.login-form .ant-form-item-has-error .ant-input-password:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 图标样式 */
.login-form .anticon {
  color: #666 !important;
}

.login-form .ant-input-affix-wrapper:focus .anticon,
.login-form .ant-input-password:focus .anticon {
  color: #333 !important;
}

/* 版权信息样式 */
.login-footer .ant-typography {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* 确保所有Ant Design组件都遵循黑白主题 */
.login-form .ant-form-item-explain-error {
  color: #ff4d4f !important;
}

.login-form .ant-form-item-label > label {
  color: #333 !important;
}

/* 覆盖任何可能的蓝色主题色 */
.ant-btn-primary {
  background: linear-gradient(135deg, #333 0%, #000 100%) !important;
  border-color: transparent !important;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background: linear-gradient(135deg, #555 0%, #333 100%) !important;
  border-color: transparent !important;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #333 !important;
  box-shadow: 0 0 0 2px rgba(51, 51, 51, 0.2) !important;
}
